import { useState, useEffect } from "react";
import {
  EyeIcon,
  TrashIcon,
  ShoppingBagIcon,
  SearchIcon,
  PlusIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Calendar,
  Mail,
  AtSignIcon,
  Phone,
  UserRoundIcon,
  Loader2,
  MapPin,
} from "lucide-react";
import { isDesktop, ripple } from "@/utils";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { allowedCountries } from "@/utils/phone-utils";
import { useFormik } from "formik";
import * as Yup from "yup";
import "yup-phone";
import { supplierService } from "@/services";
import { toast } from "sonner";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Custom styles for phone input to match login/registration style
const phoneInputStyles = `
  .supplier-phone-input {
    height: 53px;
    width: 100%;
    background: transparent;
    border: none;
    padding-left: 0;
  }

  .supplier-phone-input .PhoneInputCountryIcon {
    margin-left: 0;
  }

  .supplier-phone-input .PhoneInputCountrySelectArrow {
    opacity: 1;
    color: #64748b;
  }

  .supplier-phone-input input {
    height: 100%;
    border: none;
    outline: none;
    padding-right: 12px;
    padding-left: 1px;
    background: transparent;
    font-size: 1rem;
    color: #334155;
    transition: all 0.3s;
  }

  @media (min-width: 1024px) {
    .supplier-phone-input input {
      font-size: 1.125rem;
    }
  }
`;

// Helper function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

export const Suppliers = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [loadingAdd, setLoadingAdd] = useState(false);
  const [currentPage, setCurrentPage] = useState(0); // API uses 0-based pagination
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const itemsPerPage = 6; // Show 6 suppliers per page

  // Define validation schema using Yup
  const SupplierSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(2, "First name must be at least 2 characters")
      .required("First name is required"),
    lastName: Yup.string()
      .min(2, "Last name must be at least 2 characters")
      .required("Last name is required"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    phone: Yup.string()
      .test("phone", "Invalid phone number", function (value) {
        if (!value) return false;

        // Basic international phone number validation
        // This regex validates most international phone numbers
        // It allows for different country codes and formats
        const internationalPattern = /^\+[1-9]\d{1,14}$/;

        // Special case for Moroccan numbers (including local format)
        const moroccanPattern = /^(\+212|0)[5-7][0-9]{8}$/;

        return internationalPattern.test(value) || moroccanPattern.test(value);
      })
      .required("Phone number is required"),
    localization: Yup.string()
      .min(2, "Localization must be at least 2 characters")
      .required("Localization is required"),
  });

  // Fetch suppliers from API
  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const response = await supplierService.getAllSuppliers({
        query: searchQuery,
        page: currentPage,
        size: itemsPerPage,
      });

      if (response.success) {
        // Handle different possible data structures
        let supplierData = response.data;
        let paginationInfo = { totalPages: 1, totalElements: 0 };

        // If data has pagination structure
        if (
          supplierData &&
          supplierData.content &&
          Array.isArray(supplierData.content)
        ) {
          paginationInfo = {
            totalPages: supplierData.totalPages || 1,
            totalElements: supplierData.totalElements || 0,
          };
          supplierData = supplierData.content;
        } else if (!Array.isArray(supplierData)) {
          // If it's a single object, wrap it in an array
          if (typeof supplierData === "object") {
            supplierData = [supplierData];
          } else {
            // If we can't determine the structure, set an empty array
            supplierData = [];
          }
        }

        // Transform supplier data to match component expectations
        const transformedSuppliers = supplierData.map((supplier) => ({
          id: supplier.id,
          name: `${supplier.firstName || ""} ${supplier.lastName || ""}`.trim(),
          email: supplier.email || "",
          phone: supplier.phoneNumber || "",
          localization: supplier.localization || "N/A",
          joinedDate: supplier.addedDate
            ? formatDate(supplier.addedDate)
            : "Unknown",
          profileImage:
            supplier.profilePictureUrl || "/img/default-profile.jpg",
          // Keep original data for API operations
          rawData: supplier,
        }));

        setSuppliers(transformedSuppliers);
        setTotalPages(paginationInfo.totalPages);
        setTotalElements(paginationInfo.totalElements);
      } else {
        toast.error("Failed to fetch suppliers");
      }
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      toast.error(error.response?.data?.message || "Failed to fetch suppliers");
    } finally {
      setLoading(false);
    }
  };

  // Load suppliers when component mounts or when search/pagination changes
  useEffect(() => {
    fetchSuppliers();
  }, [currentPage, searchQuery]);

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    // API uses 0-based pagination, but UI is 1-based
    setCurrentPage(pageNumber - 1);
  };

  // Handle view supplier
  const handleViewSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setIsViewDialogOpen(true);
  };

  // Handle delete supplier
  const handleDeleteSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete supplier
  const confirmDeleteSupplier = async () => {
    if (selectedSupplier) {
      try {
        setLoadingDelete(true);
        await supplierService.deleteSupplier(selectedSupplier.id);
        toast.success("Supplier deleted successfully");
        setIsDeleteDialogOpen(false);
        // Refresh the supplier list
        fetchSuppliers();
      } catch (error) {
        console.error("Error deleting supplier:", error);
        toast.error(
          error.response?.data?.message || "Failed to delete supplier"
        );
      } finally {
        setLoadingDelete(false);
      }
    }
  };

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    setCurrentPage(0); // Reset to first page when searching
  };

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      localization: "",
    },
    validationSchema: SupplierSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        setLoadingAdd(true);

        // Prepare data for API
        const supplierData = {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phoneNumber: values.phone, // API expects phoneNumber, not phone
          localization: values.localization,
        };

        // Call API to add supplier
        await supplierService.addSupplier(supplierData);

        // Show success message
        toast.success("Supplier added successfully");

        // Close dialog and reset form
        setIsAddDialogOpen(false);
        resetForm();

        // Refresh supplier list
        fetchSuppliers();
      } catch (error) {
        console.error("Error adding supplier:", error);
        toast.error(error.response?.data?.message || "Failed to add supplier");
      } finally {
        setLoadingAdd(false);
      }
    },
  });

  // Handle opening add supplier dialog
  const handleOpenAddDialog = () => {
    formik.resetForm();
    setIsAddDialogOpen(true);
  };

  // Handle phone input change
  const handlePhoneChange = (value) => {
    formik.setFieldValue("phone", value || "");
    formik.setFieldTouched("phone", true, false);
  };

  return (
    <div className="min-h-screen mx-auto space-y-4">
      <style dangerouslySetInnerHTML={{ __html: phoneInputStyles }} />
      {/* Page header with title and search */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col gap-4">
          {/* Title Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900 flex items-center gap-2">
                <ShoppingBagIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
                <span className="truncate">Suppliers Management</span>
              </h1>
              <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
                View and manage supplier accounts
              </p>
            </div>

            {/* Add Supplier Button - Desktop */}
            <div className="hidden sm:block flex-shrink-0">
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={handleOpenAddDialog}
                className="group cursor-pointer text-sm sm:text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-700 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg whitespace-nowrap"
                type="button"
              >
                <PlusIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-700 size-4 lg:size-5 flex-shrink-0" />
                <span>Add Supplier</span>
              </button>
            </div>
          </div>

          {/* Controls Section */}
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search input */}
            <div className="flex-1 sm:max-w-xs">
              <div className="flex items-center w-full border border-blue-gray-500 rounded-xl focus-within:border-blue-gray-800 transition-all h-[48px] sm:h-[53px]">
                <SearchIcon className="ml-3 size-4 sm:size-5 lg:size-6 text-blue-gray-500 flex-shrink-0" />
                <input
                  type="text"
                  className="flex-1 h-full text-sm sm:text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                  placeholder="Search suppliers..."
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>

            {/* Add Supplier Button - Mobile */}
            <div className="sm:hidden">
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={handleOpenAddDialog}
                className="group cursor-pointer text-sm text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-700 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker w-full"
                type="button"
              >
                <PlusIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-700 size-4 flex-shrink-0" />
                <span>Add Supplier</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Supplier Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          // Loading state
          <div className="col-span-3 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
              <p className="text-blue-gray-500 text-lg">Loading suppliers...</p>
            </div>
          </div>
        ) : suppliers.length > 0 ? (
          // Suppliers list
          suppliers.map((supplier) => (
            <Card
              key={supplier.id}
              className="overflow-hidden border-blue-gray-200 rounded-xl hover:shadow-md transition-all duration-300 group"
            >
              <div>
                {/* Profile Image */}
                <div className="w-16 h-16 sm:w-18 sm:h-18 rounded-full overflow-hidden border-2 border-blue-gray-100 mx-auto mb-3">
                  <img
                    src={supplier.profileImage}
                    alt={supplier.name}
                    className="w-full h-full object-cover bg-blue-gray-200"
                  />
                </div>

                {/* Name */}
                <h3 className="text-base sm:text-lg font-semibold text-blue-gray-700 text-center mb-2">
                  <div className="truncate" title={supplier.name}>
                    {supplier.name}
                  </div>
                </h3>

                {/* Contact Info */}
                <div className="space-y-1 mb-3">
                  <p className="text-blue-gray-600 text-xs sm:text-sm text-center">
                    <span className="truncate block" title={supplier.email}>
                      {supplier.email}
                    </span>
                  </p>
                  <p className="text-blue-gray-600 text-xs sm:text-sm text-center">
                    {supplier.phone}
                  </p>
                  <div className="flex items-center justify-center gap-1 text-blue-gray-600 text-xs sm:text-sm">
                    <MapPin className="h-3 w-3 text-blue-gray-400 flex-shrink-0" />
                    <span className="truncate" title={supplier.localization}>
                      {supplier.localization}
                    </span>
                  </div>
                </div>

                {/* Join Date Badge */}
                <div className="flex justify-center mb-3">
                  <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs bg-blue-gray-50 text-blue-gray-600 border border-blue-gray-200">
                    <Calendar className="h-3 w-3 mr-1" />
                    {supplier.joinedDate}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-center pt-4 px-4 gap-2 border-t border-blue-gray-100">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleViewSupplier(supplier)}
                    className="flex-1 py-2 px-3 text-green-600 rounded-lg hover:bg-green-50 transition-all duration-300 flex items-center justify-center gap-1 text-sm font-medium"
                    type="button"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="hidden sm:inline">View</span>
                  </button>
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleDeleteSupplier(supplier)}
                    className="flex-1 py-2 px-3 text-red-600 rounded-lg hover:bg-red-50 transition-all duration-300 flex items-center justify-center gap-1 text-sm font-medium"
                    type="button"
                  >
                    <TrashIcon className="h-4 w-4" />
                    <span className="hidden sm:inline">Delete</span>
                  </button>
                </div>
              </div>
            </Card>
          ))
        ) : (
          // No suppliers found
          <div className="col-span-3 py-12 text-center">
            <p className="text-blue-gray-500 text-lg">
              No suppliers found matching your search.
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 mt-6">
          <div className="flex justify-center items-center gap-2">
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              disabled={currentPage === 0} // API uses 0-based pagination
              onClick={() => handlePageChange(currentPage + 1 - 1)} // +1 for UI, -1 for actual page
              className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                currentPage === 0
                  ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                  : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
              }`}
              type="button"
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Previous
            </button>

            <div className="flex gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onMouseDown={(event) =>
                      ripple.create(event, isDesktop() ? "light" : "dark")
                    }
                    onClick={() => handlePageChange(page)}
                    className={`w-9 h-9 p-0 rounded-lg transition-all duration-300 ${
                      currentPage + 1 === page
                        ? "bg-blue-gray-900 text-white border border-blue-gray-800"
                        : "border border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                    }`}
                    type="button"
                  >
                    {page}
                  </button>
                )
              )}
            </div>

            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              disabled={currentPage + 1 === totalPages} // +1 for UI display
              onClick={() => handlePageChange(currentPage + 1 + 1)} // +1 for UI, +1 for next page
              className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                currentPage + 1 === totalPages
                  ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                  : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
              }`}
              type="button"
            >
              Next
              <ChevronRightIcon className="h-4 w-4" />
            </button>
          </div>

          <div className="text-center text-blue-gray-500 mt-4 text-sm">
            Showing page {currentPage + 1} of {totalPages}
            {totalElements > 0 && ` (${totalElements} total suppliers)`}
          </div>
        </div>
      )}

      {/* View Supplier Dialog */}
      {selectedSupplier && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-md p-0 overflow-hidden">
            <div className="max-h-[85vh] overflow-y-auto scrollbar-thin scrollbar-thumb-blue-gray-200 hover:scrollbar-thumb-blue-gray-300 scrollbar-track-transparent scrollbar-thumb-rounded-md px-4 md:px-6 pt-4 md:pt-6 pb-6">
              <DialogHeader className="pb-2">
                <DialogTitle className="text-center text-xl font-bold text-blue-gray-900">
                  Supplier Details
                </DialogTitle>
              </DialogHeader>

              <div className="flex flex-col items-center">
                {/* Profile Image and Name */}
                <div className="w-24 h-24 sm:w-28 sm:h-28 rounded-full overflow-hidden border-4 border-blue-100 mb-3 shadow-md">
                  <img
                    src={selectedSupplier.profileImage}
                    alt={selectedSupplier.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-blue-gray-900 mb-1">
                  {selectedSupplier.name}
                </h3>

                {/* Contact Information */}
                <div className="grid gap-2 w-full mt-3">
                  <div className="bg-blue-gray-50 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <div>
                        <p className="text-xs font-medium text-blue-gray-500">
                          Email
                        </p>
                        <p className="font-medium text-blue-gray-900 text-sm break-all">
                          {selectedSupplier.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-gray-50 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <div>
                        <p className="text-xs font-medium text-blue-gray-500">
                          Phone
                        </p>
                        <p className="font-medium text-blue-gray-900 text-sm">
                          {selectedSupplier.phone}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-gray-50 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-purple-600 flex-shrink-0" />
                      <div>
                        <p className="text-xs font-medium text-blue-gray-500">
                          Localization
                        </p>
                        <p className="font-medium text-blue-gray-900 text-sm">
                          {selectedSupplier.localization}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-gray-50 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-amber-600 flex-shrink-0" />
                      <div>
                        <p className="text-xs font-medium text-blue-gray-500">
                          Joined Date
                        </p>
                        <p className="font-medium text-blue-gray-900 text-sm">
                          {selectedSupplier.joinedDate}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Close Button */}
              <div className="pt-4 mt-2">
                <button
                  onMouseDown={(event) =>
                    ripple.create(event, isDesktop() ? "light" : "dark")
                  }
                  onClick={() => setIsViewDialogOpen(false)}
                  className="w-full text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-700 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg"
                  type="button"
                >
                  Close
                </button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      {selectedSupplier && (
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-md p-0 overflow-hidden">
            <div className="max-h-[85vh] overflow-y-auto scrollbar-thin scrollbar-thumb-blue-gray-200 hover:scrollbar-thumb-blue-gray-300 scrollbar-track-transparent scrollbar-thumb-rounded-md px-4 md:px-6 pt-4 md:pt-6 pb-6">
              <DialogHeader className="pb-2">
                <DialogTitle className="text-center text-lg font-bold text-blue-gray-900">
                  Confirm Deletion
                </DialogTitle>
              </DialogHeader>

              <div>
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-14 h-14 rounded-full overflow-hidden border-2 border-red-100 flex-shrink-0">
                    <img
                      src={selectedSupplier.profileImage}
                      alt={selectedSupplier.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-blue-gray-900 text-base">
                      {selectedSupplier.name}
                    </h3>
                    <p
                      className="text-xs text-blue-gray-500 truncate max-w-[200px]"
                      title={selectedSupplier.email}
                    >
                      {selectedSupplier.email}
                    </p>
                  </div>
                </div>

                <div className="bg-red-50 border border-red-100 rounded-lg p-3 mb-4">
                  <div className="flex items-start gap-2">
                    <div className="mt-0.5 text-red-600 flex-shrink-0">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-5 h-5"
                      >
                        <path
                          fillRule="evenodd"
                          d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="text-sm text-red-700">
                      Are you sure you want to delete this supplier? This action
                      cannot be undone.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    onMouseDown={(event) =>
                      ripple.create(event, isDesktop() ? "light" : "dark")
                    }
                    onClick={() => setIsDeleteDialogOpen(false)}
                    disabled={loadingDelete}
                    className={`flex-1 px-4 py-3 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors text-base font-medium ${
                      loadingDelete
                        ? "cursor-not-allowed opacity-50"
                        : "hover:bg-blue-gray-50"
                    }`}
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    onMouseDown={(event) =>
                      ripple.create(event, isDesktop() ? "light" : "dark")
                    }
                    onClick={confirmDeleteSupplier}
                    disabled={loadingDelete}
                    className={`flex-1 px-4 py-3 rounded-xl bg-red-600 text-white transition-colors flex items-center justify-center gap-2 text-base font-medium ${
                      loadingDelete
                        ? "cursor-not-allowed opacity-50"
                        : "hover:bg-red-700"
                    }`}
                    type="button"
                  >
                    {loadingDelete ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Deleting...
                      </>
                    ) : (
                      <>
                        <TrashIcon className="h-4 w-4" />
                        Delete
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Add Supplier Dialog */}
      <Dialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            formik.resetForm();
          }
          setIsAddDialogOpen(open);
        }}
      >
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          <div className="max-h-[85vh] overflow-y-auto scrollbar-thin scrollbar-thumb-blue-gray-200 hover:scrollbar-thumb-blue-gray-300 scrollbar-track-transparent scrollbar-thumb-rounded-md px-4 md:px-6 pt-4 md:pt-6 pb-6">
            <DialogHeader className="pb-4">
              <DialogTitle className="text-center text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Add New Supplier
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={formik.handleSubmit} className="space-y-4">
              {/* First Name */}
              <div className="flex flex-col gap-y-1 w-full">
                <p className="text-base text-blue-gray-700">First Name</p>
                <div
                  className={`flex items-center w-full border ${
                    formik.touched.firstName && formik.errors.firstName
                      ? "border-red-500"
                      : "border-blue-gray-500"
                  } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
                >
                  <UserRoundIcon
                    className={`ml-3 size-5 lg:size-6 ${
                      formik.values.firstName
                        ? "text-blue-gray-700"
                        : "text-blue-gray-500"
                    } ${
                      formik.touched.firstName && formik.errors.firstName
                        ? "text-red-500"
                        : ""
                    }`}
                    onMouseDown={(event) => event.preventDefault()}
                  />
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    className="flex-1 h-full text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                    placeholder="Enter first name"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled={loadingAdd}
                  />
                </div>
                {formik.touched.firstName && formik.errors.firstName && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.firstName}
                  </p>
                )}
              </div>

              {/* Last Name */}
              <div className="flex flex-col gap-y-1 w-full">
                <p className="text-base text-blue-gray-700">Last Name</p>
                <div
                  className={`flex items-center w-full border ${
                    formik.touched.lastName && formik.errors.lastName
                      ? "border-red-500"
                      : "border-blue-gray-500"
                  } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
                >
                  <UserRoundIcon
                    className={`ml-3 size-5 lg:size-6 ${
                      formik.values.lastName
                        ? "text-blue-gray-700"
                        : "text-blue-gray-500"
                    } ${
                      formik.touched.lastName && formik.errors.lastName
                        ? "text-red-500"
                        : ""
                    }`}
                    onMouseDown={(event) => event.preventDefault()}
                  />
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    className="flex-1 h-full text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                    placeholder="Enter last name"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled={loadingAdd}
                  />
                </div>
                {formik.touched.lastName && formik.errors.lastName && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.lastName}
                  </p>
                )}
              </div>

              {/* Email */}
              <div className="flex flex-col gap-y-1 w-full">
                <p className="text-base text-blue-gray-700">Email Address</p>
                <div
                  className={`flex items-center w-full border ${
                    formik.touched.email && formik.errors.email
                      ? "border-red-500"
                      : "border-blue-gray-500"
                  } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
                >
                  <AtSignIcon
                    className={`ml-3 size-5 lg:size-6 ${
                      formik.values.email ? "text-blue-gray-700" : "text-blue-gray-500"
                    } ${
                      formik.touched.email && formik.errors.email
                        ? "text-red-500"
                        : ""
                    }`}
                    onMouseDown={(event) => event.preventDefault()}
                  />
                  <input
                    id="email"
                    name="email"
                    type="email"
                    className="flex-1 h-full text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                    placeholder="Enter email address"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled={loadingAdd}
                  />
                </div>
                {formik.touched.email && formik.errors.email && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.email}
                  </p>
                )}
              </div>

              {/* Phone Number */}
              <div className="flex flex-col gap-y-1 w-full">
                <p className="text-base text-blue-gray-700">Phone Number</p>
                <div
                  className={`w-full border ${
                    formik.touched.phone && formik.errors.phone
                      ? "border-red-500"
                      : "border-blue-gray-500"
                  } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] overflow-hidden`}
                >
                  <PhoneInput
                    international
                    defaultCountry="MA"
                    className="custom-phone-input"
                    countries={allowedCountries}
                    value={formik.values.phone}
                    onChange={handlePhoneChange}
                    onBlur={() => formik.setFieldTouched("phone", true)}
                    placeholder="Enter phone number"
                  />
                </div>
                {formik.touched.phone && formik.errors.phone && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.phone}
                  </p>
                )}
              </div>

              {/* Localization */}
              <div className="flex flex-col gap-y-1 w-full">
                <p className="text-base text-blue-gray-700">Localization</p>
                <div
                  className={`flex items-center w-full border ${
                    formik.touched.localization && formik.errors.localization
                      ? "border-red-500"
                      : "border-blue-gray-500"
                  } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
                >
                  <MapPin
                    className={`ml-3 size-5 lg:size-6 ${
                      formik.values.localization
                        ? "text-blue-gray-700"
                        : "text-blue-gray-500"
                    } ${
                      formik.touched.localization && formik.errors.localization
                        ? "text-red-500"
                        : ""
                    }`}
                    onMouseDown={(event) => event.preventDefault()}
                  />
                  <input
                    id="localization"
                    name="localization"
                    type="text"
                    className="flex-1 h-full text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                    placeholder="Enter supplier location"
                    value={formik.values.localization}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled={loadingAdd}
                  />
                </div>
                {formik.touched.localization && formik.errors.localization && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.localization}
                  </p>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 mt-6">
                <button
                  onMouseDown={(event) => ripple.create(event, "dark")}
                  type="button"
                  onClick={() => setIsAddDialogOpen(false)}
                  className="flex-1 px-4 py-3 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors hover:bg-blue-gray-50 text-base font-medium"
                >
                  Cancel
                </button>
                <button
                  onMouseDown={(event) => ripple.create(event, "dark")}
                  type="submit"
                  disabled={loadingAdd || formik.isSubmitting}
                  className={`flex-1 text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-700 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg ${
                    loadingAdd || formik.isSubmitting
                      ? "opacity-70 cursor-not-allowed"
                      : ""
                  }`}
                >
                  {loadingAdd ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Adding...</span>
                    </>
                  ) : (
                    <>
                      <PlusIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-700 h-4 w-4" />
                      <span>Add Supplier</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Suppliers;
