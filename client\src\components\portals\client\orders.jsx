import { useState, useEffect } from "react";
import {
  EyeIcon,
  PackageIcon,
  Loader2,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  CheckCircleIcon,
  TruckIcon,
} from "lucide-react";
import { orderService } from "@/services/order-service";
import { toast } from "sonner";
import { isDesktop, ripple } from "@/utils";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [pagination, setPagination] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0,
  });

  useEffect(() => {
    fetchOrders();
  }, [pagination.page]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await orderService.getMyOrders({
        page: pagination.page,
        size: pagination.size,
        sortBy: "orderDate",
        sortDir: "desc",
      });

      if (response.success) {
        setOrders(response.data.content);
        setPagination((prev) => ({
          ...prev,
          totalElements: response.data.totalElements,
          totalPages: response.data.totalPages,
        }));
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to load orders");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "PENDING":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "COMPLETED":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-blue-gray-100 text-blue-gray-800 border-blue-gray-200";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "PENDING":
        return <ClockIcon className="h-4 w-4" />;
      case "IN_PROGRESS":
        return <TruckIcon className="h-4 w-4" />;
      case "COMPLETED":
        return <CheckCircleIcon className="h-4 w-4" />;
      default:
        return <PackageIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const viewOrderDetails = (order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
          <p className="text-blue-gray-500 text-lg">Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mx-auto space-y-4">
      {/* Page header */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex items-center gap-2">
          <PackageIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900">
              My Orders
            </h1>
            <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
              Track and manage your orders
            </p>
          </div>
        </div>
      </div>

      {/* Orders List */}
      {orders.length === 0 ? (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-8 text-center">
          <PackageIcon className="h-16 w-16 mx-auto text-blue-gray-300 mb-4" />
          <h3 className="text-lg font-semibold text-blue-gray-900 mb-2">
            No orders found
          </h3>
          <p className="text-blue-gray-500">You haven't placed any orders yet.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {orders.map((order) => (
            <Card
              key={order.id}
              className="overflow-hidden border-blue-gray-200 rounded-xl hover:shadow-md transition-all duration-300"
            >
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-blue-gray-900">
                        Order #{order.id.slice(-8)}
                      </h3>
                      <div
                        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                          order.status
                        )}`}
                      >
                        {getStatusIcon(order.status)}
                        <span>{order.status}</span>
                      </div>
                    </div>
                    <p className="text-sm text-blue-gray-500">
                      Placed on {formatDate(order.orderDate)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">
                      {order.totalAmount.toFixed(2)} MAD
                    </p>
                    <p className="text-sm text-blue-gray-500">
                      {order.items.length} item
                      {order.items.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Order Items Preview */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-blue-gray-700 mb-2">
                    Items:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {order.items.slice(0, 3).map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 bg-blue-gray-50 border border-blue-gray-200 rounded-lg px-3 py-2"
                      >
                        <div className="w-6 h-6 bg-white rounded border border-blue-gray-100 overflow-hidden flex-shrink-0">
                          <img
                            src={
                              item.productImageUrl || "/img/default-product.jpg"
                            }
                            alt={item.productLabel}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <span className="text-sm text-blue-gray-700 truncate max-w-[120px]">
                          {item.productLabel} x{item.quantity}
                        </span>
                      </div>
                    ))}
                    {order.items.length > 3 && (
                      <div className="flex items-center px-3 py-2 bg-blue-gray-50 border border-blue-gray-200 rounded-lg">
                        <span className="text-sm text-blue-gray-500">
                          +{order.items.length - 3} more
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action */}
                <div className="flex justify-end pt-4 border-t border-blue-gray-100">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => viewOrderDetails(order)}
                    className="flex items-center gap-2 px-4 py-2 text-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm font-medium"
                    type="button"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span>View Details</span>
                  </button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <button
            onMouseDown={(event) => ripple.create(event, "dark")}
            onClick={() =>
              setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
            }
            disabled={pagination.page === 0}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
              pagination.page === 0
                ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
            }`}
            type="button"
          >
            <ChevronLeftIcon className="h-4 w-4" />
            <span>Previous</span>
          </button>

          <span className="text-blue-gray-600 font-medium">
            Page {pagination.page + 1} of {pagination.totalPages}
          </span>

          <button
            onMouseDown={(event) => ripple.create(event, "dark")}
            onClick={() =>
              setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
            }
            disabled={pagination.page >= pagination.totalPages - 1}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
              pagination.page >= pagination.totalPages - 1
                ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
            }`}
            type="button"
          >
            <span>Next</span>
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Order Details Modal */}
      {selectedOrder && (
        <Dialog open={showOrderDetails} onOpenChange={setShowOrderDetails}>
          <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <PackageIcon className="h-5 w-5 text-blue-gray-700" />
                Order Details
              </DialogTitle>
              <p className="text-sm text-blue-gray-500 mt-1">
                Order #{selectedOrder.id}
              </p>
            </DialogHeader>

            <div className="space-y-6">
              {/* Order Info */}
              <div>
                <h3 className="text-lg font-semibold text-blue-gray-900 mb-4">
                  Order Information
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-blue-gray-700 mb-1">
                      Status
                    </p>
                    <div
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                        selectedOrder.status
                      )}`}
                    >
                      {getStatusIcon(selectedOrder.status)}
                      <span>{selectedOrder.status}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-gray-700 mb-1">
                      Total Amount
                    </p>
                    <p className="text-xl font-bold text-green-600">
                      {selectedOrder.totalAmount.toFixed(2)} MAD
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-gray-700 mb-1">
                      Order Date
                    </p>
                    <p className="text-blue-gray-600">
                      {formatDate(selectedOrder.orderDate)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-gray-700 mb-1">
                      Phone Number
                    </p>
                    <p className="text-blue-gray-600">
                      {selectedOrder.phoneNumber}
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h3 className="text-lg font-semibold text-blue-gray-900 mb-4">
                  Order Items
                </h3>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-4 p-4 border border-blue-gray-200 rounded-lg bg-blue-gray-50"
                    >
                      <div className="w-16 h-16 bg-white rounded-lg border border-blue-gray-100 overflow-hidden flex-shrink-0">
                        <img
                          src={
                            item.productImageUrl || "/img/default-product.jpg"
                          }
                          alt={item.productLabel}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-blue-gray-900 mb-1">
                          {item.productLabel}
                        </h4>
                        {item.productBrand && (
                          <p className="text-sm text-blue-gray-600 mb-1">
                            Brand: {item.productBrand}
                          </p>
                        )}
                        <p className="text-sm text-blue-gray-500">
                          Quantity: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-blue-gray-900">
                          {item.totalPrice.toFixed(2)} MAD
                        </p>
                        <p className="text-sm text-blue-gray-500">
                          {item.unitPrice.toFixed(2)} MAD each
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Notes */}
              {selectedOrder.notes && (
                <div>
                  <h3 className="text-lg font-semibold text-blue-gray-900 mb-2">
                    Notes
                  </h3>
                  <p className="text-blue-gray-600 bg-blue-gray-50 p-3 rounded-lg border border-blue-gray-200">
                    {selectedOrder.notes}
                  </p>
                </div>
              )}

              {/* Close Button */}
              <div className="flex justify-end pt-4 border-t border-blue-gray-200">
                <button
                  onMouseDown={(event) => ripple.create(event, "dark")}
                  onClick={() => setShowOrderDetails(false)}
                  className="px-6 py-3 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors text-base font-medium hover:bg-blue-gray-50"
                  type="button"
                >
                  Close
                </button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Orders;
