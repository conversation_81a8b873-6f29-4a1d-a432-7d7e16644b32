{"name": "@material-tailwind/react", "homepage": "https://material-tailwind.com", "version": "2.1.10", "description": "@material-tailwind/react is an easy-to-use components library for ReactJS & Tailwind CSS inspired by Material Design.", "repository": "https://github.com/creativetimofficial/material-tailwind", "license": "MIT", "main": "index.js", "typings": "index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "swc ./src -d ./dist --only ./src/**/*.ts --config-file .swcrc", "build:dts": "tsc --declaration --declarationMap --emitDeclarationOnly", "copy": "cp ./package.json ./dist && cp ./README.md ./dist && cp ./LICENSE ./dist", "build": "pnpm build:cjs && pnpm build:dts && pnpm copy", "lint:check": "eslint . --ext js,jsx,ts,tsx --max-warnings=0 --config .eslintrc.json --no-eslintrc", "prettier:check": "prettier -c ."}, "dependencies": {"@floating-ui/react": "0.19.0", "classnames": "2.3.2", "deepmerge": "4.2.2", "framer-motion": "6.5.1", "material-ripple-effects": "2.0.1", "prop-types": "15.8.1", "react": "18.2.0", "react-dom": "18.2.0", "tailwind-merge": "1.8.1"}, "peerDependencies": {"react": "^16 || ^17 || ^18", "react-dom": "^16 || ^17 || ^18"}, "devDependencies": {"@swc/cli": "0.1.59", "@swc/core": "1.3.24", "@types/node": "18.11.18", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@typescript-eslint/eslint-plugin": "5.47.1", "@typescript-eslint/parser": "5.47.1", "eslint": "8.31.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.31.11", "eslint-plugin-react-hooks": "4.6.0", "path": "0.12.7", "prettier": "2.8.1", "typescript": "4.9.4"}, "keywords": ["material tailwind", "material tailwind react", "javascript", "react", "next", "create react app", "remix", "vite", "gatsby", "tailwind", "tailwind components", "tailwind elements", "tailwind library", "react library", "react components library", "tailwind sections", "tailwind css", "tailwind ui", "tailwind css react", "tailwind css next", "tailwind css remix", "tailwind css vite", "tailwind css gatsby"]}