{"name": "@floating-ui/react-dom", "version": "1.3.0", "@rollingversions": {"baseVersion": [0, 1, 0]}, "description": "Floating UI for React DOM", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.react-dom.umd.js", "module": "./dist/floating-ui.react-dom.esm.js", "unpkg": "./dist/floating-ui.react-dom.umd.min.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "module": "./dist/floating-ui.react-dom.esm.js", "import": "./dist/floating-ui.react-dom.mjs", "default": "./dist/floating-ui.react-dom.umd.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist/", "index.d.ts", "src/**/*.d.ts"], "scripts": {"test": "jest test", "build": "NODE_ENV=build rollup -c"}, "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/react-dom"}, "homepage": "https://floating-ui.com/docs/react-dom", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning", "react", "react-dom"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@floating-ui/dom": "^1.2.1"}, "devDependencies": {"@babel/preset-react": "^7.16.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/react": "^13.2.0", "@types/react": "^18.0.1", "react": "^18.0.0", "react-dom": "^18.0.0", "use-isomorphic-layout-effect": "^1.1.1"}}